<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#3b82f6", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <link rel="stylesheet" href="Shared-Assets/CSS/assets-styles-common.css">
    <link rel="stylesheet" href="Shared-Assets/CSS/assets-styles-home.css">
    <style>
      /* Hero section dark mode fix */
      body.dark .hero-title {
        color: #ffffff !important;
      }

      body.dark .hero-description {
        color: #e5e5e5 !important;
      }
    </style>

    <script>
      // Simple hero text visibility fix
      function forceHeroTextVisibility() {
        const heroTitle = document.querySelector('.hero-title');
        const heroDescription = document.querySelector('.hero-description');

        if (document.body.classList.contains('dark')) {
          if (heroTitle) {
            heroTitle.style.color = '#ffffff';
          }
          if (heroDescription) {
            heroDescription.style.color = '#e5e5e5';
          }
        } else {
          // Light mode - remove inline styles to let CSS take over
          if (heroTitle) {
            heroTitle.style.color = '';
          }
          if (heroDescription) {
            heroDescription.style.color = '';
          }
        }
      }

      // Run immediately and on theme change
      document.addEventListener('DOMContentLoaded', function() {
        forceHeroTextVisibility();
        // Run again after a short delay to ensure everything is loaded
        setTimeout(forceHeroTextVisibility, 100);
      });

      // Also run when dark mode is toggled
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.attributeName === 'class') {
            forceHeroTextVisibility();
          }
        });
      });

      observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
      });
    </script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white shadow-sm">
      <div
        class="container mx-auto px-4 py-4 flex justify-between items-center"
      >
        <div class="relative group">
          <a
            href="#"
            id="brandLogo"
            class="text-2xl font-['Pacifico'] text-primary flex items-center gap-2"
          >
            VB
            <i
              class="ri-arrow-down-s-line text-lg transition-transform group-hover:rotate-180"
            ></i>
          </a>
          <div
            class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50"
          >
            <a
              href="#home"
              class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary"
            >
              <i class="ri-home-line w-5 h-5 mr-2"></i>
              Home
            </a>
            <a
              href="#about"
              class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary"
            >
              <i class="ri-user-line w-5 h-5 mr-2"></i>
              About
            </a>
            <a
              href="#projects"
              class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary"
            >
              <i class="ri-folder-line w-5 h-5 mr-2"></i>
              Projects
            </a>
            <a
              href="#skills"
              class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary"
            >
              <i class="ri-tools-line w-5 h-5 mr-2"></i>
              Skills
            </a>
            <a
              href="#contact"
              class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary"
            >
              <i class="ri-mail-line w-5 h-5 mr-2"></i>
              Contact
            </a>
          </div>
        </div>
        <div class="hidden md:flex items-center space-x-8">
          <a
            href="#home"
            class="text-gray-800 hover:text-primary font-medium transition-colors"
            >Home</a
          >
          <a
            href="#about"
            class="text-gray-800 hover:text-primary font-medium transition-colors"
            >About</a
          >
          <a
            href="#projects"
            class="text-gray-800 hover:text-primary font-medium transition-colors"
            >Projects</a
          >
          <a
            href="#skills"
            class="text-gray-800 hover:text-primary font-medium transition-colors"
            >Skills</a
          >
          <a
            href="#contact"
            class="text-gray-800 hover:text-primary font-medium transition-colors"
            >Contact</a
          >
          <div class="flex items-center space-x-2">
            <span id="themeStatus" class="text-xs text-gray-500 hidden md:block"></span>
            <label class="switch flex items-center relative group" title="Toggle dark mode">
              <input type="checkbox" id="themeToggle" />
              <span class="slider relative">
                <i class="ri-sun-line absolute left-1 top-1/2 transform -translate-y-1/2 text-xs text-yellow-500 transition-opacity duration-300"></i>
                <i class="ri-moon-line absolute right-1 top-1/2 transform -translate-y-1/2 text-xs text-blue-400 transition-opacity duration-300"></i>
              </span>
            </label>
          </div>
        </div>
        <button
          class="md:hidden w-10 h-10 flex items-center justify-center text-gray-700"
          id="mobileMenuButton"
        >
          <i class="ri-menu-line ri-lg"></i>
        </button>
      </div>
      <!-- Mobile Menu -->
      <div
        id="mobileMenu"
        class="hidden md:hidden bg-white shadow-lg absolute w-full"
      >
        <div class="container mx-auto px-4 py-4 flex flex-col space-y-4">
          <a
            href="#home"
            class="text-gray-800 hover:text-primary font-medium transition-colors py-2 border-b border-gray-100"
            >Home</a
          >
          <a
            href="#about"
            class="text-gray-800 hover:text-primary font-medium transition-colors py-2 border-b border-gray-100"
            >About</a
          >
          <a
            href="#projects"
            class="text-gray-800 hover:text-primary font-medium transition-colors py-2 border-b border-gray-100"
            >Projects</a
          >
          <a
            href="#skills"
            class="text-gray-800 hover:text-primary font-medium transition-colors py-2 border-b border-gray-100"
            >Skills</a
          >
          <a
            href="#contact"
            class="text-gray-800 hover:text-primary font-medium transition-colors py-2 border-b border-gray-100"
            >Contact</a
          >
          <div class="flex items-center justify-between py-2">
            <span class="text-gray-700 flex items-center">
              <i class="ri-palette-line mr-2"></i>
              Dark Mode
            </span>
            <label class="switch flex items-center relative" title="Toggle dark mode">
              <input type="checkbox" id="mobileThemeToggle" />
              <span class="slider relative">
                <i class="ri-sun-line absolute left-1 top-1/2 transform -translate-y-1/2 text-xs text-yellow-500 transition-opacity duration-300"></i>
                <i class="ri-moon-line absolute right-1 top-1/2 transform -translate-y-1/2 text-xs text-blue-400 transition-opacity duration-300"></i>
              </span>
            </label>
          </div>
        </div>
      </div>
    </header>
    <!-- Hero Section -->
    <section
      id="home"
      class="relative overflow-hidden"
      style="background-image: url('https://readdy.ai/api/search-image?query=A%20modern%2C%20minimalist%20workspace%20with%20a%20sleek%20laptop%2C%20soft%20ambient%20lighting%2C%20and%20a%20clean%20desk%20setup.%20The%20left%20side%20has%20a%20gradient%20from%20light%20to%20transparent%2C%20creating%20a%20perfect%20space%20for%20text%20overlay.%20The%20right%20side%20shows%20the%20workspace%20in%20detail.%20The%20image%20has%20a%20professional%2C%20creative%20atmosphere%20with%20soft%20blue%20accents%2C%20perfect%20for%20a%20portfolio%20hero%20section&width=1920&height=1080&seq=hero-bg-1&orientation=landscape'); background-size: cover; background-position: center;"
    >
      <!-- Light mode gradient -->
      <div
        class="absolute inset-0 bg-gradient-to-r from-white via-white/90 to-transparent"
      ></div>
      <!-- Dark mode overlay -->
      <div
        class="absolute inset-0 bg-gradient-to-r from-gray-900/95 via-gray-900/90 to-gray-900/60 opacity-0 dark:opacity-100 transition-opacity duration-300"
      ></div>
      <div class="container mx-auto px-4 py-24 md:py-32 relative">
        <div class="w-full md:w-1/2">
          <h1
            class="hero-title text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6"
          >
            Hi, I'm Vivek Bukka
          </h1>
          <p class="hero-description text-xl md:text-2xl text-gray-700 dark:text-gray-200 mb-8">
            Full-stack Developer & UI/UX Designer crafting exceptional digital
            experiences
          </p>
          <div class="flex flex-col sm:flex-row gap-4 mb-10">
            <a
              href="#projects"
              class="bg-primary text-white py-3 px-8 rounded-button font-medium hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center justify-center"
            >
              <i class="ri-eye-line mr-2"></i>
              View Projects
            </a>
            <a
              href="#contact"
              class="bg-white dark:bg-gray-800 text-gray-800 dark:text-white py-3 px-8 rounded-button font-medium border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors whitespace-nowrap flex items-center justify-center"
            >
              <i class="ri-mail-line mr-2"></i>
              Contact Me
            </a>
          </div>
          <div class="flex space-x-5">
            <a
              href="https://github.com/bvivek2148"
              id="githubLink"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-github-fill"></i>
            </a>
            <div
              id="githubDialog"
              class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50"
            >
              <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-xl font-semibold">GitHub Repositories</h3>
                  <button
                    id="closeGithubDialog"
                    class="text-gray-500 hover:text-gray-700"
                  >
                    <i class="ri-close-line ri-lg"></i>
                  </button>
                </div>
                <div class="space-y-4 mb-6">
                  <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-medium mb-2">E-commerce Platform</h4>
                    <p class="text-gray-600 text-sm mb-2">
                      Full-featured online store with modern tech stack
                    </p>
                    <div class="flex gap-2">
                      <span
                        class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded"
                        >React</span
                      >
                      <span
                        class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded"
                        >Node.js</span
                      >
                    </div>
                  </div>
                  <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-medium mb-2">Analytics Dashboard</h4>
                    <p class="text-gray-600 text-sm mb-2">
                      Real-time data visualization platform
                    </p>
                    <div class="flex gap-2">
                      <span
                        class="bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded"
                        >Vue.js</span
                      >
                      <span
                        class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded"
                        >D3.js</span
                      >
                    </div>
                  </div>
                </div>
                <a
                  href="https://github.com/bvivek2148"
                  target="_blank"
                  class="w-full bg-primary text-white py-3 px-6 rounded-button font-medium hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center justify-center"
                >
                  <i class="ri-github-fill mr-2"></i>
                  Visit Full Profile
                </a>
              </div>
            </div>
            <a
              href="#"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-linkedin-fill"></i>
            </a>
            <a
              href="#"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-twitter-x-fill"></i>
            </a>
            <a
              href="#"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-dribbble-fill"></i>
            </a>
          </div>
        </div>
      </div>
    </section>
    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-16">
          About Me
        </h2>
        <div class="flex flex-col md:flex-row gap-12 items-center">
          <div class="md:w-2/5">
            <img
              src="https://readdy.ai/api/search-image?query=Professional%20portrait%20of%20a%20young%20male%20developer%20with%20a%20friendly%20smile%2C%20wearing%20smart%20casual%20attire.%20The%20image%20has%20a%20clean%2C%20minimal%20background%20with%20soft%20lighting.%20The%20subject%20has%20a%20confident%20yet%20approachable%20demeanor%2C%20perfect%20for%20a%20portfolio%20about%20section.%20High%20quality%2C%20professional%20photography%20style&width=600&height=800&seq=about-portrait&orientation=portrait"
              alt="Vivek Bukka"
              class="rounded-xl shadow-lg w-full h-auto object-cover object-top"
            />
          </div>
          <div class="md:w-3/5">
            <h3 class="text-2xl font-semibold mb-4">
              Full-stack Developer & UI/UX Designer
            </h3>
            <p class="text-gray-700 mb-6">
              With over 7 years of experience in web development and design, I
              specialize in creating elegant, user-centered digital experiences.
              My approach combines technical expertise with creative
              problem-solving to build applications that are both beautiful and
              functional.
            </p>
            <p class="text-gray-700 mb-8">
              Based in San Francisco, I've collaborated with startups and
              established companies across various industries, helping them
              achieve their digital transformation goals through thoughtful
              design and robust development.
            </p>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
              <div class="bg-gray-50 p-6 rounded-lg">
                <div
                  class="w-12 h-12 flex items-center justify-center bg-blue-100 text-primary rounded-lg mb-4"
                >
                  <i class="ri-code-s-slash-line ri-xl"></i>
                </div>
                <h4 class="text-lg font-semibold mb-2">Development</h4>
                <p class="text-gray-600">
                  Building responsive, accessible, and performant web
                  applications using modern technologies.
                </p>
              </div>
              <div class="bg-gray-50 p-6 rounded-lg">
                <div
                  class="w-12 h-12 flex items-center justify-center bg-indigo-100 text-secondary rounded-lg mb-4"
                >
                  <i class="ri-layout-4-line ri-xl"></i>
                </div>
                <h4 class="text-lg font-semibold mb-2">UI/UX Design</h4>
                <p class="text-gray-600">
                  Creating intuitive interfaces and meaningful user experiences
                  through thoughtful design.
                </p>
              </div>
            </div>
            <a
              href="#"
              id="downloadResumeBtn"
              class="inline-flex items-center bg-primary text-white py-3 px-8 rounded-button font-medium hover:bg-primary/90 transition-colors whitespace-nowrap"
            >
              <i class="ri-download-line mr-2"></i>
              Download Resume
            </a>
            <div
              id="resumeDialog"
              class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50"
            >
              <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-xl font-semibold text-gray-900">
                    Download Resume
                  </h3>
                  <button
                    id="closeResumeDialog"
                    class="text-gray-500 hover:text-gray-700"
                  >
                    <i class="ri-close-line ri-lg"></i>
                  </button>
                </div>
                <p class="text-gray-600 mb-6">
                  Select your preferred format to download Vivek's resume:
                </p>
                <div class="space-y-4">
                  <button
                    id="downloadPDF"
                    class="w-full bg-gray-50 hover:bg-gray-100 text-gray-800 py-3 px-4 rounded-button font-medium transition-colors flex items-center justify-between"
                  >
                    <span class="flex items-center">
                      <i class="ri-file-pdf-line mr-2 text-red-500"></i>
                      PDF Format
                    </span>
                    <i class="ri-download-line"></i>
                  </button>
                  <button
                    id="downloadWord"
                    class="w-full bg-gray-50 hover:bg-gray-100 text-gray-800 py-3 px-4 rounded-button font-medium transition-colors flex items-center justify-between"
                  >
                    <span class="flex items-center">
                      <i class="ri-file-word-line mr-2 text-blue-500"></i>
                      Word Format
                    </span>
                    <i class="ri-download-line"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-6">
          My Projects
        </h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-16">
          Explore my recent work across web development, UI/UX design, and
          creative coding projects.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Project 1 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://readdy.ai/api/search-image?query=A%20modern%20e-commerce%20website%20interface%20displayed%20on%20a%20laptop%20screen.%20The%20design%20is%20clean%20and%20minimalist%20with%20product%20cards%2C%20navigation%2C%20and%20shopping%20cart%20elements%20visible.%20The%20website%20showcases%20clothing%20or%20tech%20products%20with%20a%20professional%2C%20contemporary%20design.%20The%20image%20has%20a%20clean%20background%20and%20professional%20lighting%20to%20highlight%20the%20screen%20details&width=800&height=600&seq=project1&orientation=landscape"
                alt="E-commerce Platform"
                class="w-full h-full object-cover object-top transition-transform duration-500"
              />
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="projects/family-clothing-store/index.html"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Family Clothing Store</h3>
              <p class="text-gray-600 mb-4">
                A modern e-commerce platform for family clothing with responsive design,
                product catalog, and seamless shopping experience.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >HTML5</span
                >
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >CSS3</span
                >
                <span
                  class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Tailwind CSS</span
                >
              </div>
            </div>
          </div>
          <!-- Project 2 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://readdy.ai/api/search-image?query=A%20mobile%20app%20interface%20for%20a%20fitness%20tracking%20application%20shown%20on%20a%20smartphone.%20The%20screen%20displays%20workout%20statistics%2C%20activity%20tracking%2C%20and%20health%20metrics%20with%20a%20clean%2C%20modern%20UI.%20The%20design%20features%20graphs%2C%20progress%20indicators%2C%20and%20navigation%20elements.%20The%20image%20has%20a%20professional%20composition%20with%20the%20phone%20against%20a%20minimal%20background&width=800&height=600&seq=project2&orientation=landscape"
                alt="Fitness Tracking App"
                class="w-full h-full object-cover object-top transition-transform duration-500"
              />
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="projects/fitness-tracking-app/index.html"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Fitness Tracking App</h3>
              <p class="text-gray-600 mb-4">
                Comprehensive fitness tracking application with workout monitoring,
                nutrition tracking, and health metrics with dark mode support.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >HTML5</span
                >
                <span
                  class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >CSS3</span
                >
                <span
                  class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >JavaScript</span
                >
              </div>
            </div>
          </div>
          <!-- Project 3 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://readdy.ai/api/search-image?query=A%20dashboard%20interface%20for%20a%20data%20analytics%20platform%20shown%20on%20a%20computer%20screen.%20The%20interface%20displays%20various%20charts%2C%20graphs%2C%20and%20data%20visualization%20elements%20with%20a%20clean%2C%20professional%20design.%20The%20dashboard%20includes%20metrics%2C%20KPIs%2C%20and%20interactive%20elements.%20The%20image%20has%20a%20modern%2C%20corporate%20feel%20with%20professional%20lighting%20and%20composition&width=800&height=600&seq=project3&orientation=landscape"
                alt="Analytics Dashboard"
                class="w-full h-full object-cover object-top transition-transform duration-500"
              />
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="projects/analytics-dashboard/index.html"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Analytics Dashboard</h3>
              <p class="text-gray-600 mb-4">
                Comprehensive analytics solution with data visualization, real-time monitoring,
                and predictive analytics for business intelligence.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >React.js</span
                >
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >D3.js</span
                >
                <span
                  class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Python</span
                >
              </div>
            </div>
          </div>
          <!-- Project 4 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://readdy.ai/api/search-image?query=A%20content%20management%20system%20interface%20displayed%20on%20a%20desktop%20screen.%20The%20CMS%20shows%20a%20blog%20post%20editor%20with%20formatting%20tools%2C%20media%20library%2C%20and%20publishing%20options.%20The%20interface%20has%20a%20clean%2C%20professional%20design%20with%20navigation%20elements%20and%20content%20organization%20features.%20The%20image%20has%20good%20lighting%20and%20composition%20to%20highlight%20the%20screen%20details&width=800&height=600&seq=project4&orientation=landscape"
                alt="Content Management System"
                class="w-full h-full object-cover object-top transition-transform duration-500"
              />
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="#"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                  onclick="alert('This project is coming soon!'); return false;"
                >
                  Coming Soon
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">
                Content Management System
              </h3>
              <p class="text-gray-600 mb-4">
                Custom CMS with intuitive content editing, media management, and
                flexible templating system.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs font-medium px-2.5 py-0.5 rounded"
                  >PHP</span
                >
                <span
                  class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2.5 py-0.5 rounded"
                  >MySQL</span
                >
                <span
                  class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs font-medium px-2.5 py-0.5 rounded"
                  >JavaScript</span
                >
              </div>
            </div>
          </div>

          <!-- Project 6 - Task Management App -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="TaskFlow Project Management"
                class="w-full h-full object-cover object-center transition-transform duration-500"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <div class="w-full h-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center" style="display: none;">
                <div class="text-center text-white">
                  <i class="ri-task-line text-6xl mb-4"></i>
                  <h3 class="text-2xl font-bold mb-2">TaskFlow</h3>
                  <p class="text-lg opacity-90">Project Management</p>
                  <div class="flex justify-center space-x-4 mt-4 text-sm opacity-75">
                    <span>📋 Kanban Board</span>
                    <span>👥 Team Collaboration</span>
                    <span>📊 Progress Tracking</span>
                  </div>
                </div>
              </div>
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="projects/task-management-app/index.html"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">TaskFlow - Project Management</h3>
              <p class="text-gray-600 mb-4">
                Comprehensive project management application with Kanban boards,
                task tracking, team collaboration, and progress monitoring.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >React</span
                >
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >TypeScript</span
                >
                <span
                  class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Tailwind CSS</span
                >
                <span
                  class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Drag & Drop</span
                >
              </div>
            </div>
          </div>

          <!-- Project 7 - Weather Dashboard -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group hidden-projects"
            style="display: none;"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="WeatherPro Dashboard"
                class="w-full h-full object-cover object-center transition-transform duration-500"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <div class="w-full h-full bg-gradient-to-br from-blue-400 to-cyan-500 flex items-center justify-center" style="display: none;">
                <div class="text-center text-white">
                  <i class="ri-cloud-line text-6xl mb-4"></i>
                  <h3 class="text-2xl font-bold mb-2">WeatherPro</h3>
                  <p class="text-lg opacity-90">Weather Dashboard</p>
                  <div class="flex justify-center space-x-4 mt-4 text-sm opacity-75">
                    <span>🌤️ Live Weather</span>
                    <span>📈 Charts</span>
                    <span>🌍 Global Data</span>
                  </div>
                </div>
              </div>
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="projects/weather-dashboard/index.html"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">WeatherPro Dashboard</h3>
              <p class="text-gray-600 mb-4">
                Advanced weather dashboard with real-time data, forecasts,
                air quality monitoring, and interactive charts.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Vue.js</span
                >
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Chart.js</span
                >
                <span
                  class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Weather API</span
                >
                <span
                  class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Geolocation</span
                >
              </div>
            </div>
          </div>

          <!-- Project 8 - Social Media Dashboard -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group hidden-projects"
            style="display: none;"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="SocialPulse Analytics"
                class="w-full h-full object-cover object-center transition-transform duration-500"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <div class="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center" style="display: none;">
                <div class="text-center text-white">
                  <i class="ri-bar-chart-line text-6xl mb-4"></i>
                  <h3 class="text-2xl font-bold mb-2">SocialPulse</h3>
                  <p class="text-lg opacity-90">Social Analytics</p>
                  <div class="flex justify-center space-x-4 mt-4 text-sm opacity-75">
                    <span>📊 Analytics</span>
                    <span>📱 Multi-Platform</span>
                    <span>📈 Growth Tracking</span>
                  </div>
                </div>
              </div>
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="projects/social-media-dashboard/index.html"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">SocialPulse Analytics</h3>
              <p class="text-gray-600 mb-4">
                Comprehensive social media analytics dashboard with multi-platform
                integration, engagement tracking, and performance insights.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Angular</span
                >
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Chart.js</span
                >
                <span
                  class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Social APIs</span
                >
                <span
                  class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Real-time Data</span
                >
              </div>
            </div>
          </div>

          <!-- E-commerce Platform Project -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group hidden-projects"
            style="display: none;"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="E-commerce Platform"
                class="w-full h-full object-cover object-center transition-transform duration-500"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <div class="w-full h-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center" style="display: none;">
                <div class="text-center text-white">
                  <i class="ri-store-line text-6xl mb-4"></i>
                  <h3 class="text-2xl font-bold mb-2">E-commerce Platform</h3>
                  <p class="text-lg opacity-90">Online Shopping Solution</p>
                  <div class="flex justify-center space-x-4 mt-4 text-sm opacity-75">
                    <span>🛒 Shopping Cart</span>
                    <span>💳 Secure Payments</span>
                    <span>📱 Mobile-First</span>
                  </div>
                </div>
              </div>
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="projects/E-commerce Platform/index.html"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">E-commerce Platform</h3>
              <p class="text-gray-600 mb-4">
                Comprehensive online shopping solution for FashionTrend Retail with advanced product management, secure payments, and exceptional user experience.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >React.js</span
                >
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Node.js</span
                >
                <span
                  class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >MongoDB</span
                >
                <span
                  class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Stripe</span
                >
              </div>
            </div>
          </div>

          <!-- Travel Planning App Project -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md project-card group hidden-projects"
            style="display: none;"
          >
            <div class="relative overflow-hidden h-64">
              <img
                src="https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Travel Planning App"
                class="w-full h-full object-cover object-center transition-transform duration-500"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <div class="w-full h-full bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center" style="display: none;">
                <div class="text-center text-white">
                  <i class="ri-map-pin-line text-6xl mb-4"></i>
                  <h3 class="text-2xl font-bold mb-2">TravelPlan</h3>
                  <p class="text-lg opacity-90">Trip Planning Platform</p>
                  <div class="flex justify-center space-x-4 mt-4 text-sm opacity-75">
                    <span>🗺️ Itinerary</span>
                    <span>🏨 Bookings</span>
                    <span>📍 Recommendations</span>
                  </div>
                </div>
              </div>
              <div
                class="absolute inset-0 bg-primary/80 flex items-center justify-center opacity-0 transition-opacity duration-300 project-overlay"
              >
                <a
                  href="#"
                  class="bg-white text-primary py-2 px-6 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
                >
                  View Project
                </a>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Travel Planning App</h3>
              <p class="text-gray-600 mb-4">
                Comprehensive travel planning platform with itinerary management, booking integration, and personalized recommendations for seamless trip organization.
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >React Native</span
                >
                <span
                  class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Express.js</span
                >
                <span
                  class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Google Maps API</span
                >
                <span
                  class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >Firebase</span
                >
              </div>
            </div>
          </div>
        </div>
        <div class="text-center mt-12">
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              id="viewMoreProjectsBtn"
              class="inline-flex items-center bg-primary text-white py-3 px-8 rounded-button font-medium hover:bg-primary/90 transition-colors whitespace-nowrap"
            >
              <i class="ri-add-line mr-2"></i>
              <span id="viewMoreText">View More Projects</span>
            </button>
            <a
              href="#"
              class="inline-flex items-center bg-white text-gray-800 py-3 px-8 rounded-button font-medium border border-gray-300 hover:bg-gray-50 transition-colors whitespace-nowrap"
            >
              <i class="ri-github-line mr-2"></i>
              View More on GitHub
            </a>
          </div>
        </div>
      </div>
    </section>
    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-6">
          Skills & Technologies
        </h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-16">
          A comprehensive overview of my technical skills and the technologies I
          work with.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
          <!-- Skills Charts -->
          <div>
            <h3 class="text-xl font-semibold mb-6">Technical Proficiency</h3>
            <div class="space-y-6">
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-gray-700 font-medium"
                    >Frontend Development</span
                  >
                  <span class="text-gray-500">95%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="bg-primary h-2.5 rounded-full"
                    style="width: 95%"
                  ></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-gray-700 font-medium"
                    >Backend Development</span
                  >
                  <span class="text-gray-500">85%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="bg-primary h-2.5 rounded-full"
                    style="width: 85%"
                  ></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-gray-700 font-medium">UI/UX Design</span>
                  <span class="text-gray-500">90%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="bg-primary h-2.5 rounded-full"
                    style="width: 90%"
                  ></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-gray-700 font-medium"
                    >Mobile Development</span
                  >
                  <span class="text-gray-500">80%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="bg-primary h-2.5 rounded-full"
                    style="width: 80%"
                  ></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-gray-700 font-medium"
                    >Database Management</span
                  >
                  <span class="text-gray-500">85%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="bg-primary h-2.5 rounded-full"
                    style="width: 85%"
                  ></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-gray-700 font-medium"
                    >DevOps & Deployment</span
                  >
                  <span class="text-gray-500">75%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="bg-primary h-2.5 rounded-full"
                    style="width: 75%"
                  ></div>
                </div>
              </div>
            </div>
          </div>
          <!-- Technology Icons -->
          <div>
            <h3 class="text-xl font-semibold mb-6">Technologies I Work With</h3>
            <div class="grid grid-cols-3 sm:grid-cols-4 gap-6">
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-blue-100 text-blue-600 rounded-lg mb-2"
                >
                  <i class="ri-html5-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">HTML5</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-blue-100 text-blue-600 rounded-lg mb-2"
                >
                  <i class="ri-css3-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">CSS3</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-yellow-100 text-yellow-600 rounded-lg mb-2"
                >
                  <i class="ri-javascript-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">JavaScript</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-blue-100 text-blue-600 rounded-lg mb-2"
                >
                  <i class="ri-reactjs-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">React</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-green-100 text-green-600 rounded-lg mb-2"
                >
                  <i class="ri-vuejs-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">Vue.js</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-red-100 text-red-600 rounded-lg mb-2"
                >
                  <i class="ri-angularjs-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">Angular</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-green-100 text-green-600 rounded-lg mb-2"
                >
                  <i class="ri-nodejs-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">Node.js</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-blue-100 text-blue-600 rounded-lg mb-2"
                >
                  <i class="ri-database-2-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">SQL</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-green-100 text-green-600 rounded-lg mb-2"
                >
                  <i class="ri-database-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">MongoDB</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-orange-100 text-orange-600 rounded-lg mb-2"
                >
                  <i class="ri-fire-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">Firebase</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-blue-100 text-blue-600 rounded-lg mb-2"
                >
                  <i class="ri-github-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">Git</span>
              </div>
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 flex items-center justify-center bg-purple-100 text-purple-600 rounded-lg mb-2"
                >
                  <i class="ri-flutter-fill ri-2x"></i>
                </div>
                <span class="text-gray-700 text-sm">Flutter</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-6">
          Get In Touch
        </h2>
        <p class="text-gray-600 text-center max-w-2xl mx-auto mb-16">
          Have a project in mind or want to collaborate? Feel free to reach out!
        </p>
        <div class="flex flex-col lg:flex-row gap-12">
          <div class="lg:w-1/2">
            <form id="contactForm" class="bg-white p-8 rounded-lg shadow-md">
              <div class="mb-6">
                <label for="name" class="block text-gray-700 font-medium mb-2"
                  >Name</label
                >
                <input
                  type="text"
                  id="name"
                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                  placeholder="Your name"
                />
              </div>
              <div class="mb-6">
                <label for="email" class="block text-gray-700 font-medium mb-2"
                  >Email</label
                >
                <input
                  type="email"
                  id="email"
                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                  placeholder="Your email address"
                />
              </div>
              <div class="mb-6">
                <label
                  for="subject"
                  class="block text-gray-700 font-medium mb-2"
                  >Subject</label
                >
                <input
                  type="text"
                  id="subject"
                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                  placeholder="Subject of your message"
                />
              </div>
              <div class="mb-6">
                <label
                  for="message"
                  class="block text-gray-700 font-medium mb-2"
                  >Message</label
                >
                <textarea
                  id="message"
                  rows="5"
                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                  placeholder="Your message"
                ></textarea>
              </div>
              <div class="mb-6 flex items-center">
                <input
                  type="checkbox"
                  id="consent"
                  class="custom-checkbox mr-2"
                />
                <label for="consent" class="text-gray-700"
                  >I agree to the privacy policy and terms of service</label
                >
              </div>
              <button
                type="submit"
                class="w-full bg-primary text-white py-3 px-6 rounded-button font-medium hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center justify-center"
              >
                <i class="ri-send-plane-fill mr-2"></i>
                Send Message
              </button>
            </form>
          </div>
          <div class="lg:w-1/2">
            <div class="bg-white p-8 rounded-lg shadow-md h-full">
              <h3 class="text-xl font-semibold mb-6">Contact Information</h3>
              <div class="space-y-6">
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 text-primary rounded-lg mr-4 mt-1"
                  >
                    <i class="ri-map-pin-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-gray-800 font-medium">Location</h4>
                    <p class="text-gray-600">
                      123 Market Street, San Francisco, CA 94103
                    </p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 text-primary rounded-lg mr-4 mt-1"
                  >
                    <i class="ri-mail-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-gray-800 font-medium">Email</h4>
                    <p class="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 text-primary rounded-lg mr-4 mt-1"
                  >
                    <i class="ri-phone-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-gray-800 font-medium">Phone</h4>
                    <p class="text-gray-600">+****************</p>
                  </div>
                </div>
              </div>
              <div class="mt-8">
                <h4 class="text-gray-800 font-medium mb-4">Connect with me</h4>
                <div class="flex space-x-4">
                  <a
                    href="https://github.com/bvivek2148"
                    id="githubLink"
                    class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-700 hover:bg-primary hover:text-white transition-colors"
                  >
                    <i class="ri-github-fill"></i>
                  </a>
                  <div
                    id="githubDialog"
                    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50"
                  >
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                      <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold">
                          GitHub Repositories
                        </h3>
                        <button
                          id="closeGithubDialog"
                          class="text-gray-500 hover:text-gray-700"
                        >
                          <i class="ri-close-line ri-lg"></i>
                        </button>
                      </div>
                      <div class="space-y-4 mb-6">
                        <div class="p-4 bg-gray-50 rounded-lg">
                          <h4 class="font-medium mb-2">E-commerce Platform</h4>
                          <p class="text-gray-600 text-sm mb-2">
                            Full-featured online store with modern tech stack
                          </p>
                          <div class="flex gap-2">
                            <span
                              class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded"
                              >React</span
                            >
                            <span
                              class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded"
                              >Node.js</span
                            >
                          </div>
                        </div>
                        <div class="p-4 bg-gray-50 rounded-lg">
                          <h4 class="font-medium mb-2">Analytics Dashboard</h4>
                          <p class="text-gray-600 text-sm mb-2">
                            Real-time data visualization platform
                          </p>
                          <div class="flex gap-2">
                            <span
                              class="bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded"
                              >Vue.js</span
                            >
                            <span
                              class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded"
                              >D3.js</span
                            >
                          </div>
                        </div>
                      </div>
                      <a
                        href="https://github.com/bvivek2148"
                        target="_blank"
                        class="w-full bg-primary text-white py-3 px-6 rounded-button font-medium hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center justify-center"
                      >
                        <i class="ri-github-fill mr-2"></i>
                        Visit Full Profile
                      </a>
                    </div>
                  </div>
                  <a
                    href="#"
                    class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-700 hover:bg-primary hover:text-white transition-colors"
                  >
                    <i class="ri-linkedin-fill"></i>
                  </a>
                  <a
                    href="#"
                    class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-700 hover:bg-primary hover:text-white transition-colors"
                  >
                    <i class="ri-twitter-x-fill"></i>
                  </a>
                  <a
                    href="#"
                    class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-700 hover:bg-primary hover:text-white transition-colors"
                  >
                    <i class="ri-dribbble-fill"></i>
                  </a>
                </div>
              </div>
              <div class="mt-8">
                <h4 class="text-gray-800 font-medium mb-4">Location</h4>
                <div
                  class="w-full h-60 rounded-lg overflow-hidden"
                  style="background-image: url('https://public.readdy.ai/gen_page/map_placeholder_1280x720.png'); background-size: cover; background-position: center;"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="container mx-auto px-4">
        <div
          class="flex flex-col md:flex-row justify-between items-center mb-8"
        >
          <a
            href="#"
            id="footerLogo"
            class="text-2xl font-['Pacifico'] text-white mb-4 md:mb-0"
            >VB</a
          >
          <div class="flex flex-wrap justify-center gap-6">
            <a
              href="#home"
              class="text-gray-300 hover:text-white transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-300 hover:text-white transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-300 hover:text-white transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-300 hover:text-white transition-colors"
              >Skills</a
            >
            <a
              href="#contact"
              class="text-gray-300 hover:text-white transition-colors"
              >Contact</a
            >
          </div>
          <div class="flex space-x-4 mt-6 md:mt-0">
            <a
              href="#"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800 text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-github-fill"></i>
            </a>
            <a
              href="#"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800 text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-linkedin-fill"></i>
            </a>
            <a
              href="#"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800 text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-twitter-x-fill"></i>
            </a>
            <a
              href="#"
              class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800 text-gray-300 hover:bg-primary hover:text-white transition-colors"
            >
              <i class="ri-dribbble-fill"></i>
            </a>
          </div>
        </div>
        <div class="border-t border-gray-800 pt-8">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-400 text-sm mb-4 md:mb-0">
              © 2025 Vivek Bukka. All rights reserved.
            </p>
            <div class="flex flex-wrap justify-center gap-6">
              <a
                href="#"
                class="text-gray-400 text-sm hover:text-white transition-colors"
                >Privacy Policy</a
              >
              <a
                href="#"
                class="text-gray-400 text-sm hover:text-white transition-colors"
                >Terms of Service</a
              >
              <a
                href="#"
                class="text-gray-400 text-sm hover:text-white transition-colors"
                >Cookie Policy</a
              >
            </div>
          </div>
        </div>
      </div>
    </footer>
    <!-- Back to Top Button -->
    <button
      id="backToTop"
      class="fixed bottom-6 right-6 w-12 h-12 bg-primary text-white rounded-full shadow-lg flex items-center justify-center opacity-0 invisible transition-all duration-300"
    >
      <i class="ri-arrow-up-line ri-lg"></i>
    </button>
    <!-- Scripts -->
    <script src="Shared-Assets/JavaScript/assets-scripts-common.js"></script>
    <script src="Shared-Assets/JavaScript/assets-scripts-home.js"></script>


  </body>
</html>
